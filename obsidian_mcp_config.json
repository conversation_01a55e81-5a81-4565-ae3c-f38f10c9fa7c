{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>"], "description": "文件系统操作服务器，允许读写文件和目录操作"}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "description": "知识图谱记忆服务器，用于存储和检索信息"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "顺序思考服务器，帮助结构化问题解决过程"}, "local-filesystem": {"command": "./mcp_env/bin/python", "args": ["mcp_servers/filesystem_server.py"], "env": {"MCP_SERVER_PORT": "8001"}, "description": "本地文件系统服务器（自定义实现）"}, "local-memory": {"command": "./mcp_env/bin/python", "args": ["mcp_servers/memory_server.py"], "env": {"MCP_SERVER_PORT": "8002"}, "description": "本地记忆服务器（自定义实现）"}, "git": {"command": "./mcp_env/bin/python", "args": ["-m", "mcp_server_git", "--repository", "/home/<USER>/mcp"], "description": "Git版本控制服务器，用于管理Obsidian笔记的版本历史"}, "time": {"command": "./mcp_env/bin/python", "args": ["-m", "mcp_server_time"], "description": "时间服务器，提供时间相关功能"}, "obsidian": {"command": "uv", "args": ["--directory", "/home/<USER>/mcp/mcp-obsidian", "run", "mcp-obsidian"], "env": {"OBSIDIAN_API_KEY": "6ad3a9c097110a06208ae134658d5d32680db21ef6b16817d833ece7cf86c45a", "OBSIDIAN_HOST": "127.0.0.1", "OBSIDIAN_PORT": "27124"}, "description": "Obsidian MCP服务器，通过Local REST API与Obsidian交互"}}}