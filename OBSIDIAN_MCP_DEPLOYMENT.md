# Obsidian MCP 部署指南

本指南将详细介绍如何安装、配置和部署 Obsidian MCP 服务，使 AI 助手（如 Claude Desktop）能够与 Obsidian 笔记应用进行交互。

## 📋 概述

Obsidian MCP 服务通过 Local REST API 插件与 Obsidian 进行通信，提供以下功能：

- 列出笔记库中的文件和目录
- 读取笔记内容
- 搜索笔记内容
- 修改笔记内容
- 删除笔记
- 追加内容到笔记

## 🚀 安装步骤

### 1. 安装 Obsidian Local REST API 插件

1. 打开 Obsidian
2. 进入设置 → 社区插件
3. 禁用"安全模式"
4. 点击"浏览"搜索"Local REST API"
5. 安装并启用该插件
6. 在插件设置中生成 API 密钥并记录下来

### 2. 克隆并安装 mcp-obsidian 项目

```bash
# 克隆项目
git clone https://github.com/MarkusPfundstein/mcp-obsidian.git

# 进入项目目录
cd mcp-obsidian

# 安装依赖
uv sync
```

### 3. 配置环境变量

有两种方式配置环境变量：

#### 方式一：在 MCP 配置中直接指定（推荐）

在 [obsidian_mcp_config.json](file:///home/<USER>/mcp/obsidian_mcp_config.json) 中配置：

```json
{
  "mcpServers": {
    "obsidian": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/mcp-obsidian",
        "run",
        "mcp-obsidian"
      ],
      "env": {
        "OBSIDIAN_API_KEY": "your_api_key_here",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

#### 方式二：创建 .env 文件

在 mcp-obsidian 目录下创建 `.env` 文件：

```env
OBSIDIAN_API_KEY=your_api_key_here
OBSIDIAN_HOST=127.0.0.1
OBSIDIAN_PORT=27124
```

## ⚙️ 配置说明

### Obsidian REST API 设置

1. **API 密钥**：在 Obsidian 的 Local REST API 插件设置中生成
2. **主机地址**：默认为 `127.0.0.1`
3. **端口号**：默认为 `27124`

### MCP 服务器配置

配置文件中的关键参数：

- `command`：启动命令，使用 `uv` 运行项目
- `args`：传递给命令的参数
- `env`：环境变量配置

## ▶️ 启动和使用

### 启动 MCP 服务器

使用已创建的脚本启动所有 MCP 服务器：

```bash
./start_obsidian_mcp.sh
```

或者单独测试 Obsidian MCP 服务器：

```bash
cd mcp-obsidian
uv run mcp-obsidian
```

### 在 Claude Desktop 中配置

1. 打开 Claude Desktop 配置文件：
   - macOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`
   - Windows: `%APPDATA%/Claude/claude_desktop_config.json`

2. 添加 Obsidian MCP 服务器配置：

```json
{
  "mcpServers": {
    "obsidian": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/mcp-obsidian",
        "run",
        "mcp-obsidian"
      ],
      "env": {
        "OBSIDIAN_API_KEY": "your_api_key_here",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

## 🧪 测试功能

启动服务器后，可以在 Claude 中尝试以下操作：

1. "列出我笔记库中的所有文件"
2. "搜索包含'项目计划'的笔记"
3. "读取'会议记录.md'的内容"
4. "在'待办事项.md'中添加一项新任务"
5. "创建一个名为'新笔记.md'的笔记，内容是'这是通过MCP创建的笔记'"

## 🛠️ 故障排除

### 常见问题

1. **无法连接到 Obsidian**
   - 检查 Obsidian 是否正在运行
   - 确认 Local REST API 插件已启用
   - 验证 API 密钥是否正确

2. **MCP 服务器启动失败**
   - 检查是否已正确安装依赖
   - 确认 uv 工具是否可用
   - 查看是否有端口冲突

3. **Claude 无法识别工具**
   - 重启 Claude Desktop
   - 检查配置文件格式是否正确
   - 确认 MCP 服务器是否正常运行

### 日志查看

查看 MCP 服务器日志：

```bash
# macOS
tail -n 20 -f ~/Library/Logs/Claude/mcp-server-obsidian.log

# Linux
tail -n 20 -f ~/.local/share/Claude/logs/mcp-server-obsidian.log
```

## 🔧 高级配置

### 使用 uvx 运行（可选）

如果希望直接通过 uvx 运行（需要先发布）：

```json
{
  "mcpServers": {
    "obsidian": {
      "command": "uvx",
      "args": [
        "mcp-obsidian"
      ],
      "env": {
        "OBSIDIAN_API_KEY": "your_api_key_here",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

### 开发调试

使用 MCP Inspector 进行调试：

```bash
npx @modelcontextprotocol/inspector uv --directory /path/to/mcp-obsidian run mcp-obsidian
```

## 📚 相关资源

- [Obsidian Local REST API 插件](https://github.com/coddingtonbear/obsidian-local-rest-api)
- [mcp-obsidian GitHub 仓库](https://github.com/MarkusPfundstein/mcp-obsidian)
- [MCP 官方文档](https://modelcontextprotocol.io)
- [Obsidian 官方文档](https://help.obsidian.md)
