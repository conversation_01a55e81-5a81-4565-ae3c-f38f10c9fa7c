#!/bin/bash

# Obsidian MCP 服务器启动脚本
# 此脚本用于启动所有配置的MCP服务器，以便与Obsidian配合使用

echo "🚀 启动 Obsidian MCP 服务器..."

# 检查Python虚拟环境
if [ ! -d "mcp_env" ]; then
    echo "❌ Python虚拟环境不存在，请先运行 setup_mcp.sh"
    exit 1
fi

# 激活虚拟环境
source mcp_env/bin/activate

echo "✅ Python虚拟环境已激活"

# 启动MCP服务器
echo "🔄 启动所有MCP服务器..."
python start_mcp_servers.py start

echo "✅ Obsidian MCP服务器已启动"
echo ""
echo "📝 使用说明："
echo "1. 确保Obsidian已安装并运行"
echo "2. 在Obsidian中安装必要的插件（如Local REST API）"
echo "3. 将 obsidian_mcp_config.json 中的配置添加到Obsidian的MCP设置中"
echo "4. 重启Obsidian以加载MCP服务器"
echo ""
echo "🛑 要停止服务器，请运行: ./stop_obsidian_mcp.sh"