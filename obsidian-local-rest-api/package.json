{"name": "obsidian-local-rest-api", "version": "3.2.0", "description": "Get, change or otherwise interact with your notes in Obsidian via a REST API.", "main": "main.js", "types": "main.d.ts", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "build-docs": "jsonnet -J docs/src/lib/ -S docs/src/openapi.jsonnet -o docs/openapi.yaml", "serve-docs": "sh -c 'docker run -p 8080:8080 -e SWAGGER_JSON=/mnt/openapi.yaml -v $(pwd)/docs/openapi.yaml:/mnt/openapi.yaml swaggerapi/swagger-ui'", "version": "node version-bump.mjs && git add manifest.json versions.json", "test": "jest"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@types/cors": "^2.8.12", "@types/express": "^4.17.13", "@types/glob-to-regexp": "^0.4.1", "@types/jest": "^27.4.0", "@types/json-logic-js": "^1.2.1", "@types/mime-types": "^2.1.1", "@types/node": "^16.11.6", "@types/node-forge": "^1.0.0", "@types/supertest": "^2.0.11", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "builtin-modules": "^3.3.0", "esbuild": "0.13.2", "jest": "^27", "jest-raw-loader": "^1.0.1", "obsidian": "latest", "supertest": "^6.2.2", "ts-jest": "27", "tslib": "2.4.0", "typescript": "4.7.4"}, "dependencies": {"@types/response-time": "^2.3.5", "cors": "^2.8.5", "express": "^4.21.1", "glob-to-regexp": "^0.4.1", "json-logic-js": "^2.0.2", "markdown-patch": "^0.2.2", "matcher": "^5.0.0", "mime-types": "^2.1.35", "minimatch": "^5.0.1", "node-forge": "^1.2.1", "obsidian-daily-notes-interface": "^0.9.4", "obsidian-dataview": "^0.5.47", "query-string": "^7.1.1", "response-time": "^2.3.2", "uuid": "^8.3.2"}}