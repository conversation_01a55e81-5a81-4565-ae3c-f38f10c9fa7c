components:
  schemas:
    Error:
      properties:
        errorCode:
          description: |
            A 5-digit error code uniquely identifying this particular type of error.
          example: 40149
          type: "number"
        message:
          description: "Message describing the error."
          example: "A brief description of the error."
          type: "string"
      type: "object"
    NoteJson:
      properties:
        content:
          type: "string"
        frontmatter:
          type: "object"
        path:
          type: "string"
        stat:
          properties:
            ctime:
              type: "number"
            mtime:
              type: "number"
            size:
              type: "number"
          required:
            - "ctime"
            - "mtime"
            - "size"
          type: "object"
        tags:
          items:
            type: "string"
          type: "array"
      required:
        - "tags"
        - "frontmatter"
        - "stat"
        - "path"
        - "content"
      type: "object"
  securitySchemes:
    apiKeyAuth:
      description: |
        Find your API Key in your Obsidian settings
        in the "Local REST API" section under "Plugins".
      scheme: "bearer"
      type: "http"
info:
  description: |
    You can use this interface for trying out your Local REST API in Obsidian.
    
    Before trying the below tools, you will want to make sure you press the "Authorize" button below and provide the API Key you are shown when you open the "Local REST API" section of your Obsidian settings.  All requests to the API require a valid API Key; so you won't get very far without doing that.
    
    When using this tool you may see browser security warnings due to your browser not trusting the self-signed certificate the plugin will generate on its first run.  If you do, you can make those errors disappear by adding the certificate as a "Trusted Certificate" in your browser or operating system's settings.
  title: "Local REST API for Obsidian"
  version: "1.0"
openapi: "3.0.2"
paths:
  /:
    get:
      description: |
        Returns basic details about the server as well as your authentication status.
        
        This is the only API request that does *not* require authentication.
      responses:
        "200":
          content:
            application/json:
              schema:
                properties:
                  authenticated:
                    description: "Is your current request authenticated?"
                    type: "boolean"
                  ok:
                    description: "'OK'"
                    type: "string"
                  service:
                    description: "'Obsidian Local REST API'"
                    type: "string"
                  versions:
                    properties:
                      obsidian:
                        description: "Obsidian plugin API version"
                        type: "string"
                      self:
                        description: "Plugin version."
                        type: "string"
                    type: "object"
                type: "object"
          description: "Success"
      summary: |
        Returns basic details about the server.
      tags:
        - "System"
  /active/:
    delete:
      parameters: []
      responses:
        "204":
          description: "Success"
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "File does not exist."
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Deletes the currently-active file in Obsidian.
      tags:
        - "Active File"
    get:
      description: |
        Returns the content of the currently active file in Obsidian.
        
        If you specify the header `Accept: application/vnd.olrapi.note+json`, will return a JSON representation of your note including parsed tag and frontmatter data as well as filesystem metadata.  See "responses" below for details.
      parameters: []
      responses:
        "200":
          content:
            "application/vnd.olrapi.note+json":
              schema:
                "$ref": "#/components/schemas/NoteJson"
            text/markdown:
              schema:
                example: |
                  # This is my document
                  
                  something else here
                type: "string"
          description: "Success"
        "404":
          description: "File does not exist"
      summary: |
        Return the content of the active file open in Obsidian.
      tags:
        - "Active File"
    patch:
      description: |
        Inserts content into the currently-open note relative to a heading, block refeerence, or frontmatter field within that document.
        
        Allows you to modify the content relative to a heading, block reference, or frontmatter field in your document.
        
        Note that this API was changed in Version 3.0 of this extension and the earlier PATCH API is now deprecated. Requests made using the previous version of this API will continue to work until Version 4.0 is released.  See https://github.com/coddingtonbear/obsidian-local-rest-api/wiki/Changes-to-PATCH-requests-between-versions-2.0-and-3.0 for more details and migration instructions.
        
        # Examples
        
        All of the below examples assume you have a document that looks like
        this:
        
        ```markdown
        ---
        alpha: 1
        beta: test
        delta:
        zeta: 1
        yotta: 1
        gamma:
        - one
        - two
        ---
        
        # Heading 1
        
        This is the content for heading one
        
        Also references some [[#^484ef2]]
        
        ## Subheading 1:1
        Content for Subheading 1:1
        
        ### Subsubheading 1:1:1
        
        ### Subsubheading 1:1:2
        
        Testing how block references work for a table.[[#^2c7cfa]]
        Some content for Subsubheading 1:1:2
        
        More random text.
        
        ^2d9b4a
        
        ## Subheading 1:2
        
        Content for Subheading 1:2.
        
        some content with a block reference ^484ef2
        
        ## Subheading 1:3
        | City         | Population |
        | ------------ | ---------- |
        | Seattle, WA  | 8          |
        | Portland, OR | 4          |
        
        ^2c7cfa
        ```
        
        ## Append Content Below a Heading
        
        If you wanted to append the content "Hello" below "Subheading 1:1:1" under "Heading 1",
        you could send a request with the following headers:
        
        - `Operation`: `append`
        - `Target-Type`: `heading`
        - `Target`: `Heading 1::Subheading 1:1:1`
        - with the request body: `Hello`
        
        The above would work just fine for `prepend` or `replace`, too, of course,
        but with different results.
        
        ## Append Content to a Block Reference
        
        If you wanted to append the content "Hello" below the block referenced by
        "2d9b4a" above ("More random text."), you could send the following headers:
        
        - `Operation`: `append`
        - `Target-Type`: `block`
        - `Target`: `2d9b4a`
        - with the request body: `Hello`
        
        The above would work just fine for `prepend` or `replace`, too, of course,
        but with different results.
        
        ## Add a Row to a Table Referenced by a Block Reference
        
        If you wanted to add a new city ("Chicago, IL") and population ("16") pair to the table above
        referenced by the block reference `2c7cfa`, you could send the following
        headers:
        
        - `Operation`: `append`
        - `TargetType`: `block`
        - `Target`: `2c7cfa`
        - `Content-Type`: `application/json`
        - with the request body: `[["Chicago, IL", "16"]]`
        
        The use of a `Content-Type` of `application/json` allows the API
        to infer that member of your array represents rows and columns of your
        to append to the referenced table.  You can of course just use a
        `Content-Type` of `text/markdown`, but in such a case you'll have to
        format your table row manually instead of letting the library figure
        it out for you.
        
        You also have the option of using `prepend` (in which case, your new
        row would be the first -- right below the table heading) or `replace` (in which
        case all rows except the table heading would be replaced by the new row(s)
        you supplied).
        
        ## Setting a Frontmatter Field
        
        If you wanted to set the frontmatter field `alpha` to `2`, you could
        send the following headers:
        
        - `Operation`: `replace`
        - `TargetType`: `frontmatter`
        - `Target`: `beep`
        - with the request body `2`
        
        If you're setting a frontmatter field that might not already exist
        you may want to use the `Create-Target-If-Missing` header so the
        new frontmatter field is created and set to your specified value
        if it doesn't already exist.
        
        You may find using a `Content-Type` of `application/json` to be
        particularly useful in the case of frontmatter since frontmatter
        fields' values are JSON data, and the API can be smarter about
        interpreting yoru `prepend` or `append` requests if you specify
        your data as JSON (particularly when appending, for example,
        list items).
      parameters:
        - description: "Patch operation to perform"
          in: "header"
          name: "Operation"
          required: true
          schema:
            enum:
              - "append"
              - "prepend"
              - "replace"
            type: "string"
        - description: "Type of target to patch"
          in: "header"
          name: "Target-Type"
          required: true
          schema:
            enum:
              - "heading"
              - "block"
              - "frontmatter"
            type: "string"
        - description: "Delimiter to use for nested targets (i.e. Headings)"
          in: "header"
          name: "Target-Delimiter"
          required: false
          schema:
            default: "::"
            type: "string"
        - description: |
            Target to patch; this value can be URL-Encoded and *must*
            be URL-Encoded if it includes non-ASCII characters.
          in: "header"
          name: "Target"
          required: true
          schema:
            type: "string"
        - description: "Trim whitespace from Target before applying patch?"
          in: "header"
          name: "Trim-Target-Whitespace"
          required: false
          schema:
            default: "false"
            enum:
              - "true"
              - "false"
            type: "string"
      requestBody:
        content:
          application/json:
            schema:
              example: "['one', 'two']"
              type: "string"
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content you would like to insert."
        required: true
      responses:
        "200":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Bad Request; see response message for details."
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Does not exist"
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Partially update content in the currently open note.
      tags:
        - "Active File"
    post:
      description: |
        Appends content to the end of the currently-open note.
        
        If you would like to insert text relative to a particular heading instead of appending to the end of the file, see 'patch'.
      parameters: []
      requestBody:
        content:
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content you would like to append."
        required: true
      responses:
        "204":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Bad Request"
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Append content to the active file open in Obsidian.
      tags:
        - "Active File"
    put:
      parameters: []
      requestBody:
        content:
          "*/*":
            schema:
              type: "string"
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content of the file you would like to upload."
        required: true
      responses:
        "204":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Incoming file could not be processed.  Make sure you have specified a reasonable file name, and make sure you have set a reasonable 'Content-Type' header; if you are uploading a note, 'text/markdown' is likely the right choice.
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Update the content of the active file open in Obsidian.
      tags:
        - "Active File"
  /commands/:
    get:
      responses:
        "200":
          content:
            application/json:
              example:
                commands:
                  - id: "global-search:open"
                    name: "Search: Search in all files"
                  - id: "graph:open"
                    name: "Graph view: Open graph view"
              schema:
                properties:
                  commands:
                    items:
                      properties:
                        id:
                          type: "string"
                        name:
                          type: "string"
                      type: "object"
                    type: "array"
                type: "object"
          description: "A list of available commands."
      summary: |
        Get a list of available commands.
      tags:
        - "Commands"
  "/commands/{commandId}/":
    post:
      parameters:
        - description: "The id of the command to execute"
          in: "path"
          name: "commandId"
          required: true
          schema:
            type: "string"
      responses:
        "204":
          description: "Success"
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "The command you specified does not exist."
      summary: |
        Execute a command.
      tags:
        - "Commands"
  /obsidian-local-rest-api.crt:
    get:
      responses:
        "200":
          description: "Success"
      summary: |
        Returns the certificate in use by this API.
      tags:
        - "System"
  "/open/{filename}":
    post:
      description: |
        Note: Obsidian will create a new document at the path you have
        specified if such a document did not already exist.
      parameters:
        - description: |
            Path to the file to return (relative to your vault root).
          in: "path"
          name: "filename"
          required: true
          schema:
            format: "path"
            type: "string"
        - description: "Open this as a new leaf?"
          in: "query"
          name: "newLeaf"
          required: false
          schema:
            type: "boolean"
      responses:
        "200":
          description: "Success"
      summary: |
        Open the specified document in the Obsidian user interface.
      tags:
        - "Open"
  /openapi.yaml:
    get:
      responses:
        "200":
          description: "Success"
      summary: |
        Returns OpenAPI YAML document describing the capabilities of this API.
      tags:
        - "System"
  "/periodic/{period}/":
    delete:
      parameters:
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      responses:
        "204":
          description: "Success"
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "File does not exist."
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Delete the current periodic note for the specified period.
      tags:
        - "Periodic Notes"
    get:
      parameters:
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      responses:
        "200":
          content:
            "application/vnd.olrapi.note+json":
              schema:
                "$ref": "#/components/schemas/NoteJson"
            text/markdown:
              schema:
                example: |
                  # This is my document
                  
                  something else here
                type: "string"
          description: "Success"
        "404":
          description: "File does not exist"
      summary: |
        Get current periodic note for the specified period.
      tags:
        - "Periodic Notes"
    patch:
      description: |
        Inserts content into the current periodic note for the specified period relative to a heading, block refeerence, or frontmatter field within that document.
        
        Allows you to modify the content relative to a heading, block reference, or frontmatter field in your document.
        
        Note that this API was changed in Version 3.0 of this extension and the earlier PATCH API is now deprecated. Requests made using the previous version of this API will continue to work until Version 4.0 is released.  See https://github.com/coddingtonbear/obsidian-local-rest-api/wiki/Changes-to-PATCH-requests-between-versions-2.0-and-3.0 for more details and migration instructions.
        
        # Examples
        
        All of the below examples assume you have a document that looks like
        this:
        
        ```markdown
        ---
        alpha: 1
        beta: test
        delta:
        zeta: 1
        yotta: 1
        gamma:
        - one
        - two
        ---
        
        # Heading 1
        
        This is the content for heading one
        
        Also references some [[#^484ef2]]
        
        ## Subheading 1:1
        Content for Subheading 1:1
        
        ### Subsubheading 1:1:1
        
        ### Subsubheading 1:1:2
        
        Testing how block references work for a table.[[#^2c7cfa]]
        Some content for Subsubheading 1:1:2
        
        More random text.
        
        ^2d9b4a
        
        ## Subheading 1:2
        
        Content for Subheading 1:2.
        
        some content with a block reference ^484ef2
        
        ## Subheading 1:3
        | City         | Population |
        | ------------ | ---------- |
        | Seattle, WA  | 8          |
        | Portland, OR | 4          |
        
        ^2c7cfa
        ```
        
        ## Append Content Below a Heading
        
        If you wanted to append the content "Hello" below "Subheading 1:1:1" under "Heading 1",
        you could send a request with the following headers:
        
        - `Operation`: `append`
        - `Target-Type`: `heading`
        - `Target`: `Heading 1::Subheading 1:1:1`
        - with the request body: `Hello`
        
        The above would work just fine for `prepend` or `replace`, too, of course,
        but with different results.
        
        ## Append Content to a Block Reference
        
        If you wanted to append the content "Hello" below the block referenced by
        "2d9b4a" above ("More random text."), you could send the following headers:
        
        - `Operation`: `append`
        - `Target-Type`: `block`
        - `Target`: `2d9b4a`
        - with the request body: `Hello`
        
        The above would work just fine for `prepend` or `replace`, too, of course,
        but with different results.
        
        ## Add a Row to a Table Referenced by a Block Reference
        
        If you wanted to add a new city ("Chicago, IL") and population ("16") pair to the table above
        referenced by the block reference `2c7cfa`, you could send the following
        headers:
        
        - `Operation`: `append`
        - `TargetType`: `block`
        - `Target`: `2c7cfa`
        - `Content-Type`: `application/json`
        - with the request body: `[["Chicago, IL", "16"]]`
        
        The use of a `Content-Type` of `application/json` allows the API
        to infer that member of your array represents rows and columns of your
        to append to the referenced table.  You can of course just use a
        `Content-Type` of `text/markdown`, but in such a case you'll have to
        format your table row manually instead of letting the library figure
        it out for you.
        
        You also have the option of using `prepend` (in which case, your new
        row would be the first -- right below the table heading) or `replace` (in which
        case all rows except the table heading would be replaced by the new row(s)
        you supplied).
        
        ## Setting a Frontmatter Field
        
        If you wanted to set the frontmatter field `alpha` to `2`, you could
        send the following headers:
        
        - `Operation`: `replace`
        - `TargetType`: `frontmatter`
        - `Target`: `beep`
        - with the request body `2`
        
        If you're setting a frontmatter field that might not already exist
        you may want to use the `Create-Target-If-Missing` header so the
        new frontmatter field is created and set to your specified value
        if it doesn't already exist.
        
        You may find using a `Content-Type` of `application/json` to be
        particularly useful in the case of frontmatter since frontmatter
        fields' values are JSON data, and the API can be smarter about
        interpreting yoru `prepend` or `append` requests if you specify
        your data as JSON (particularly when appending, for example,
        list items).
      parameters:
        - description: "Patch operation to perform"
          in: "header"
          name: "Operation"
          required: true
          schema:
            enum:
              - "append"
              - "prepend"
              - "replace"
            type: "string"
        - description: "Type of target to patch"
          in: "header"
          name: "Target-Type"
          required: true
          schema:
            enum:
              - "heading"
              - "block"
              - "frontmatter"
            type: "string"
        - description: "Delimiter to use for nested targets (i.e. Headings)"
          in: "header"
          name: "Target-Delimiter"
          required: false
          schema:
            default: "::"
            type: "string"
        - description: |
            Target to patch; this value can be URL-Encoded and *must*
            be URL-Encoded if it includes non-ASCII characters.
          in: "header"
          name: "Target"
          required: true
          schema:
            type: "string"
        - description: "Trim whitespace from Target before applying patch?"
          in: "header"
          name: "Trim-Target-Whitespace"
          required: false
          schema:
            default: "false"
            enum:
              - "true"
              - "false"
            type: "string"
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      requestBody:
        content:
          application/json:
            schema:
              example: "['one', 'two']"
              type: "string"
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content you would like to insert."
        required: true
      responses:
        "200":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Bad Request; see response message for details."
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Does not exist"
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Partially update content in the current periodic note for the specified period.
      tags:
        - "Periodic Notes"
    post:
      description: |
        Note that this will create the relevant periodic note if necessary.
      parameters:
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      requestBody:
        content:
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content you would like to append."
        required: true
      responses:
        "204":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Bad Request"
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Append content to the current periodic note for the specified period.
      tags:
        - "Periodic Notes"
    put:
      parameters:
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      requestBody:
        content:
          "*/*":
            schema:
              type: "string"
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content of the file you would like to upload."
        required: true
      responses:
        "204":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Incoming file could not be processed.  Make sure you have specified a reasonable file name, and make sure you have set a reasonable 'Content-Type' header; if you are uploading a note, 'text/markdown' is likely the right choice.
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Update the content of the current periodic note for the specified period.
      tags:
        - "Periodic Notes"
  "/periodic/{period}/{year}/{month}/{day}/":
    delete:
      description: |
        Deletes the periodic note for the specified period.
      parameters:
        - description: "The year of the date for which you would like to grab a periodic note."
          in: "path"
          name: "year"
          required: true
          schema:
            type: "number"
        - description: "The month (1-12) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "month"
          required: true
          schema:
            type: "number"
        - description: "The day (1-31) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "day"
          required: true
          schema:
            type: "number"
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      responses:
        "204":
          description: "Success"
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "File does not exist."
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Delete the periodic note for the specified period and date.
      tags:
        - "Periodic Notes"
    get:
      parameters:
        - description: "The year of the date for which you would like to grab a periodic note."
          in: "path"
          name: "year"
          required: true
          schema:
            type: "number"
        - description: "The month (1-12) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "month"
          required: true
          schema:
            type: "number"
        - description: "The day (1-31) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "day"
          required: true
          schema:
            type: "number"
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      responses:
        "200":
          content:
            "application/vnd.olrapi.note+json":
              schema:
                "$ref": "#/components/schemas/NoteJson"
            text/markdown:
              schema:
                example: |
                  # This is my document
                  
                  something else here
                type: "string"
          description: "Success"
        "404":
          description: "File does not exist"
      summary: |
        Get the periodic note for the specified period and date.
      tags:
        - "Periodic Notes"
    patch:
      description: |
        Inserts content into a periodic note relative to a heading, block refeerence, or frontmatter field within that document.
        
        Allows you to modify the content relative to a heading, block reference, or frontmatter field in your document.
        
        Note that this API was changed in Version 3.0 of this extension and the earlier PATCH API is now deprecated. Requests made using the previous version of this API will continue to work until Version 4.0 is released.  See https://github.com/coddingtonbear/obsidian-local-rest-api/wiki/Changes-to-PATCH-requests-between-versions-2.0-and-3.0 for more details and migration instructions.
        
        # Examples
        
        All of the below examples assume you have a document that looks like
        this:
        
        ```markdown
        ---
        alpha: 1
        beta: test
        delta:
        zeta: 1
        yotta: 1
        gamma:
        - one
        - two
        ---
        
        # Heading 1
        
        This is the content for heading one
        
        Also references some [[#^484ef2]]
        
        ## Subheading 1:1
        Content for Subheading 1:1
        
        ### Subsubheading 1:1:1
        
        ### Subsubheading 1:1:2
        
        Testing how block references work for a table.[[#^2c7cfa]]
        Some content for Subsubheading 1:1:2
        
        More random text.
        
        ^2d9b4a
        
        ## Subheading 1:2
        
        Content for Subheading 1:2.
        
        some content with a block reference ^484ef2
        
        ## Subheading 1:3
        | City         | Population |
        | ------------ | ---------- |
        | Seattle, WA  | 8          |
        | Portland, OR | 4          |
        
        ^2c7cfa
        ```
        
        ## Append Content Below a Heading
        
        If you wanted to append the content "Hello" below "Subheading 1:1:1" under "Heading 1",
        you could send a request with the following headers:
        
        - `Operation`: `append`
        - `Target-Type`: `heading`
        - `Target`: `Heading 1::Subheading 1:1:1`
        - with the request body: `Hello`
        
        The above would work just fine for `prepend` or `replace`, too, of course,
        but with different results.
        
        ## Append Content to a Block Reference
        
        If you wanted to append the content "Hello" below the block referenced by
        "2d9b4a" above ("More random text."), you could send the following headers:
        
        - `Operation`: `append`
        - `Target-Type`: `block`
        - `Target`: `2d9b4a`
        - with the request body: `Hello`
        
        The above would work just fine for `prepend` or `replace`, too, of course,
        but with different results.
        
        ## Add a Row to a Table Referenced by a Block Reference
        
        If you wanted to add a new city ("Chicago, IL") and population ("16") pair to the table above
        referenced by the block reference `2c7cfa`, you could send the following
        headers:
        
        - `Operation`: `append`
        - `TargetType`: `block`
        - `Target`: `2c7cfa`
        - `Content-Type`: `application/json`
        - with the request body: `[["Chicago, IL", "16"]]`
        
        The use of a `Content-Type` of `application/json` allows the API
        to infer that member of your array represents rows and columns of your
        to append to the referenced table.  You can of course just use a
        `Content-Type` of `text/markdown`, but in such a case you'll have to
        format your table row manually instead of letting the library figure
        it out for you.
        
        You also have the option of using `prepend` (in which case, your new
        row would be the first -- right below the table heading) or `replace` (in which
        case all rows except the table heading would be replaced by the new row(s)
        you supplied).
        
        ## Setting a Frontmatter Field
        
        If you wanted to set the frontmatter field `alpha` to `2`, you could
        send the following headers:
        
        - `Operation`: `replace`
        - `TargetType`: `frontmatter`
        - `Target`: `beep`
        - with the request body `2`
        
        If you're setting a frontmatter field that might not already exist
        you may want to use the `Create-Target-If-Missing` header so the
        new frontmatter field is created and set to your specified value
        if it doesn't already exist.
        
        You may find using a `Content-Type` of `application/json` to be
        particularly useful in the case of frontmatter since frontmatter
        fields' values are JSON data, and the API can be smarter about
        interpreting yoru `prepend` or `append` requests if you specify
        your data as JSON (particularly when appending, for example,
        list items).
      parameters:
        - description: "Patch operation to perform"
          in: "header"
          name: "Operation"
          required: true
          schema:
            enum:
              - "append"
              - "prepend"
              - "replace"
            type: "string"
        - description: "Type of target to patch"
          in: "header"
          name: "Target-Type"
          required: true
          schema:
            enum:
              - "heading"
              - "block"
              - "frontmatter"
            type: "string"
        - description: "Delimiter to use for nested targets (i.e. Headings)"
          in: "header"
          name: "Target-Delimiter"
          required: false
          schema:
            default: "::"
            type: "string"
        - description: |
            Target to patch; this value can be URL-Encoded and *must*
            be URL-Encoded if it includes non-ASCII characters.
          in: "header"
          name: "Target"
          required: true
          schema:
            type: "string"
        - description: "Trim whitespace from Target before applying patch?"
          in: "header"
          name: "Trim-Target-Whitespace"
          required: false
          schema:
            default: "false"
            enum:
              - "true"
              - "false"
            type: "string"
        - description: "The year of the date for which you would like to grab a periodic note."
          in: "path"
          name: "year"
          required: true
          schema:
            type: "number"
        - description: "The month (1-12) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "month"
          required: true
          schema:
            type: "number"
        - description: "The day (1-31) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "day"
          required: true
          schema:
            type: "number"
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      requestBody:
        content:
          application/json:
            schema:
              example: "['one', 'two']"
              type: "string"
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content you would like to insert."
        required: true
      responses:
        "200":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Bad Request; see response message for details."
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Does not exist"
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Partially update content in the periodic note for the specified period and date.
      tags:
        - "Periodic Notes"
    post:
      description: |
        This will create the relevant periodic note if necessary.
      parameters:
        - description: "The year of the date for which you would like to grab a periodic note."
          in: "path"
          name: "year"
          required: true
          schema:
            type: "number"
        - description: "The month (1-12) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "month"
          required: true
          schema:
            type: "number"
        - description: "The day (1-31) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "day"
          required: true
          schema:
            type: "number"
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      requestBody:
        content:
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content you would like to append."
        required: true
      responses:
        "204":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Bad Request"
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Append content to the periodic note for the specified period and date.
      tags:
        - "Periodic Notes"
    put:
      parameters:
        - description: "The year of the date for which you would like to grab a periodic note."
          in: "path"
          name: "year"
          required: true
          schema:
            type: "number"
        - description: "The month (1-12) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "month"
          required: true
          schema:
            type: "number"
        - description: "The day (1-31) of the date for which you would like to grab a periodic note."
          in: "path"
          name: "day"
          required: true
          schema:
            type: "number"
        - description: "The name of the period for which you would like to grab a periodic note."
          in: "path"
          name: "period"
          required: true
          schema:
            default: "daily"
            enum:
              - "daily"
              - "weekly"
              - "monthly"
              - "quarterly"
              - "yearly"
            type: "string"
      requestBody:
        content:
          "*/*":
            schema:
              type: "string"
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content of the file you would like to upload."
        required: true
      responses:
        "204":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Incoming file could not be processed.  Make sure you have specified a reasonable file name, and make sure you have set a reasonable 'Content-Type' header; if you are uploading a note, 'text/markdown' is likely the right choice.
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Update the content of the periodic note for the specified period and date.
      tags:
        - "Periodic Notes"
  /search/:
    post:
      description: |
        Evaluates a provided query against each file in your vault.
        
        This endpoint supports multiple query formats.  Your query should be specified in your request's body, and will be interpreted according to the `Content-type` header you specify from the below options.Additional query formats may be added in the future.
        
        # Dataview DQL (`application/vnd.olrapi.dataview.dql+txt`)
        
        Accepts a `TABLE`-type Dataview query as a text string.  See [Dataview](https://blacksmithgu.github.io/obsidian-dataview/query/queries/)'s query documentation for information on how to construct a query.
        
        # JsonLogic (`application/vnd.olrapi.jsonlogic+json`)
        
        Accepts a JsonLogic query specified as JSON.  See [JsonLogic](https://jsonlogic.com/operations.html)'s documentation for information about the base set of operators available, but in addition to those operators the following operators are available:
        
        - `glob: [PATTERN, VALUE]`: Returns `true` if a string matches a glob pattern.  E.g.: `{"glob": ["*.foo", "bar.foo"]}` is `true` and `{"glob": ["*.bar", "bar.foo"]}` is `false`.
        - `regexp: [PATTERN, VALUE]`: Returns `true` if a string matches a regular expression.  E.g.: `{"regexp": [".*\.foo", "bar.foo"]` is `true` and `{"regexp": [".*\.bar", "bar.foo"]}` is `false`.
        
        Returns only non-falsy results.  "Non-falsy" here treats the following values as "falsy":
        
        - `false`
        - `null` or `undefined`
        - `0`
        - `[]`
        - `{}`
        
        Files are represented as an object having the schema described
        in the Schema named 'NoteJson' at the bottom of this page.
        Understanding the shape of a JSON object from a schema can be
        tricky; so you may find it helpful to examine the generated metadata
        for individual files in your vault to understand exactly what values
        are returned.  To see that, access the `GET` `/vault/{filePath}`
        route setting the header:
        `Accept: application/vnd.olrapi.note+json`.  See examples below
        for working examples of queries performing common search operations.
      requestBody:
        content:
          "application/vnd.olrapi.dataview.dql+txt":
            examples:
              find_fields_by_tag:
                summary: "List data from files having the #game tag."
                value: |
                  TABLE
                    time-played AS "Time Played",
                    length AS "Length",
                    rating AS "Rating"
                  FROM #game
                  SORT rating DESC
            schema:
              externalDocs:
                url: "https://blacksmithgu.github.io/obsidian-dataview/query/queries/"
              type: "object"
          "application/vnd.olrapi.jsonlogic+json":
            examples:
              find_by_frontmatter_url_glob:
                summary: "Find notes having URL or a matching URL glob frontmatter field."
                value: |
                  {
                    "or": [
                      {"===": [{"var": "frontmatter.url"}, "https://myurl.com/some/path/"]},
                      {"glob": [{"var": "frontmatter.url-glob"}, "https://myurl.com/some/path/"]}
                    ]
                  }
              find_by_frontmatter_value:
                summary: "Find notes having a certain frontmatter field value."
                value: |
                  {
                    "==": [
                      {"var": "frontmatter.myField"},
                      "myValue"
                    ]
                  }
              find_by_tag:
                summary: "Find notes having a certain tag"
                value: |
                  {
                    "in": [
                      "myTag",
                      {"var": "tags"}
                    ]
                  }
            schema:
              externalDocs:
                url: "https://jsonlogic.com/operations.html"
              type: "object"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  properties:
                    filename:
                      description: "Path to the matching file"
                      type: "string"
                    result:
                      oneOf:
                        - type: "string"
                        - type: "number"
                        - type: "array"
                        - type: "object"
                        - type: "boolean"
                  required:
                    - "filename"
                    - "result"
                  type: "object"
                type: "array"
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Bad request.  Make sure you have specified an acceptable
            Content-Type for your search query.
      summary: |
        Search for documents matching a specified search query
      tags:
        - "Search"
  /search/simple/:
    post:
      parameters:
        - description: "Your search query"
          in: "query"
          name: "query"
          required: true
          schema:
            type: "string"
        - description: "How much context to return around the matching string"
          in: "query"
          name: "contextLength"
          required: false
          schema:
            default: 100
            type: "number"
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  properties:
                    filename:
                      description: "Path to the matching file"
                      type: "string"
                    matches:
                      items:
                        properties:
                          context:
                            type: "string"
                          match:
                            properties:
                              end:
                                type: "number"
                              start:
                                type: "number"
                            required:
                              - "start"
                              - "end"
                            type: "object"
                        required:
                          - "match"
                          - "context"
                        type: "object"
                      type: "array"
                    score:
                      type: "number"
                  type: "object"
                type: "array"
          description: "Success"
      summary: |
        Search for documents matching a specified text query
      tags:
        - "Search"
  /vault/:
    get:
      description: |
        Lists files in the root directory of your vault.
        
        Note: that this is exactly the same API endpoint as the below "List files that exist in the specified directory." and exists here only due to a quirk of this particular interactive tool.
      responses:
        "200":
          content:
            application/json:
              example:
                files:
                  - "mydocument.md"
                  - "somedirectory/"
              schema:
                properties:
                  files:
                    items:
                      type: "string"
                    type: "array"
                type: "object"
          description: "Success"
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Directory does not exist"
      summary: |
        List files that exist in the root of your vault.
      tags:
        - "Vault Directories"
  "/vault/{filename}":
    delete:
      parameters:
        - description: |
            Path to the relevant file (relative to your vault root).
          in: "path"
          name: "filename"
          required: true
          schema:
            format: "path"
            type: "string"
      responses:
        "204":
          description: "Success"
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "File does not exist."
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Delete a particular file in your vault.
      tags:
        - "Vault Files"
    get:
      description: |
        Returns the content of the file at the specified path in your vault should the file exist.
        
        If you specify the header `Accept: application/vnd.olrapi.note+json`, will return a JSON representation of your note including parsed tag and frontmatter data as well as filesystem metadata.  See "responses" below for details.
      parameters:
        - description: |
            Path to the relevant file (relative to your vault root).
          in: "path"
          name: "filename"
          required: true
          schema:
            format: "path"
            type: "string"
      responses:
        "200":
          content:
            "application/vnd.olrapi.note+json":
              schema:
                "$ref": "#/components/schemas/NoteJson"
            text/markdown:
              schema:
                example: |
                  # This is my document
                  
                  something else here
                type: "string"
          description: "Success"
        "404":
          description: "File does not exist"
      summary: |
        Return the content of a single file in your vault.
      tags:
        - "Vault Files"
    patch:
      description: |
        Inserts content into an existing note relative to a heading, block refeerence, or frontmatter field within that document.
        
        Allows you to modify the content relative to a heading, block reference, or frontmatter field in your document.
        
        Note that this API was changed in Version 3.0 of this extension and the earlier PATCH API is now deprecated. Requests made using the previous version of this API will continue to work until Version 4.0 is released.  See https://github.com/coddingtonbear/obsidian-local-rest-api/wiki/Changes-to-PATCH-requests-between-versions-2.0-and-3.0 for more details and migration instructions.
        
        # Examples
        
        All of the below examples assume you have a document that looks like
        this:
        
        ```markdown
        ---
        alpha: 1
        beta: test
        delta:
        zeta: 1
        yotta: 1
        gamma:
        - one
        - two
        ---
        
        # Heading 1
        
        This is the content for heading one
        
        Also references some [[#^484ef2]]
        
        ## Subheading 1:1
        Content for Subheading 1:1
        
        ### Subsubheading 1:1:1
        
        ### Subsubheading 1:1:2
        
        Testing how block references work for a table.[[#^2c7cfa]]
        Some content for Subsubheading 1:1:2
        
        More random text.
        
        ^2d9b4a
        
        ## Subheading 1:2
        
        Content for Subheading 1:2.
        
        some content with a block reference ^484ef2
        
        ## Subheading 1:3
        | City         | Population |
        | ------------ | ---------- |
        | Seattle, WA  | 8          |
        | Portland, OR | 4          |
        
        ^2c7cfa
        ```
        
        ## Append Content Below a Heading
        
        If you wanted to append the content "Hello" below "Subheading 1:1:1" under "Heading 1",
        you could send a request with the following headers:
        
        - `Operation`: `append`
        - `Target-Type`: `heading`
        - `Target`: `Heading 1::Subheading 1:1:1`
        - with the request body: `Hello`
        
        The above would work just fine for `prepend` or `replace`, too, of course,
        but with different results.
        
        ## Append Content to a Block Reference
        
        If you wanted to append the content "Hello" below the block referenced by
        "2d9b4a" above ("More random text."), you could send the following headers:
        
        - `Operation`: `append`
        - `Target-Type`: `block`
        - `Target`: `2d9b4a`
        - with the request body: `Hello`
        
        The above would work just fine for `prepend` or `replace`, too, of course,
        but with different results.
        
        ## Add a Row to a Table Referenced by a Block Reference
        
        If you wanted to add a new city ("Chicago, IL") and population ("16") pair to the table above
        referenced by the block reference `2c7cfa`, you could send the following
        headers:
        
        - `Operation`: `append`
        - `TargetType`: `block`
        - `Target`: `2c7cfa`
        - `Content-Type`: `application/json`
        - with the request body: `[["Chicago, IL", "16"]]`
        
        The use of a `Content-Type` of `application/json` allows the API
        to infer that member of your array represents rows and columns of your
        to append to the referenced table.  You can of course just use a
        `Content-Type` of `text/markdown`, but in such a case you'll have to
        format your table row manually instead of letting the library figure
        it out for you.
        
        You also have the option of using `prepend` (in which case, your new
        row would be the first -- right below the table heading) or `replace` (in which
        case all rows except the table heading would be replaced by the new row(s)
        you supplied).
        
        ## Setting a Frontmatter Field
        
        If you wanted to set the frontmatter field `alpha` to `2`, you could
        send the following headers:
        
        - `Operation`: `replace`
        - `TargetType`: `frontmatter`
        - `Target`: `beep`
        - with the request body `2`
        
        If you're setting a frontmatter field that might not already exist
        you may want to use the `Create-Target-If-Missing` header so the
        new frontmatter field is created and set to your specified value
        if it doesn't already exist.
        
        You may find using a `Content-Type` of `application/json` to be
        particularly useful in the case of frontmatter since frontmatter
        fields' values are JSON data, and the API can be smarter about
        interpreting yoru `prepend` or `append` requests if you specify
        your data as JSON (particularly when appending, for example,
        list items).
      parameters:
        - description: "Patch operation to perform"
          in: "header"
          name: "Operation"
          required: true
          schema:
            enum:
              - "append"
              - "prepend"
              - "replace"
            type: "string"
        - description: "Type of target to patch"
          in: "header"
          name: "Target-Type"
          required: true
          schema:
            enum:
              - "heading"
              - "block"
              - "frontmatter"
            type: "string"
        - description: "Delimiter to use for nested targets (i.e. Headings)"
          in: "header"
          name: "Target-Delimiter"
          required: false
          schema:
            default: "::"
            type: "string"
        - description: |
            Target to patch; this value can be URL-Encoded and *must*
            be URL-Encoded if it includes non-ASCII characters.
          in: "header"
          name: "Target"
          required: true
          schema:
            type: "string"
        - description: "Trim whitespace from Target before applying patch?"
          in: "header"
          name: "Trim-Target-Whitespace"
          required: false
          schema:
            default: "false"
            enum:
              - "true"
              - "false"
            type: "string"
        - description: |
            Path to the relevant file (relative to your vault root).
          in: "path"
          name: "filename"
          required: true
          schema:
            format: "path"
            type: "string"
      requestBody:
        content:
          application/json:
            schema:
              example: "['one', 'two']"
              type: "string"
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content you would like to insert."
        required: true
      responses:
        "200":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Bad Request; see response message for details."
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Does not exist"
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Partially update content in an existing note.
      tags:
        - "Vault Files"
    post:
      description: |
        Appends content to the end of an existing note. If the specified file does not yet exist, it will be created as an empty file.
        
        If you would like to insert text relative to a particular heading, block reference, or frontmatter field instead of appending to the end of the file, see 'patch'.
      parameters:
        - description: |
            Path to the relevant file (relative to your vault root).
          in: "path"
          name: "filename"
          required: true
          schema:
            format: "path"
            type: "string"
      requestBody:
        content:
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content you would like to append."
        required: true
      responses:
        "204":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Bad Request"
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Append content to a new or existing file.
      tags:
        - "Vault Files"
    put:
      description: |
        Creates a new file in your vault or updates the content of an existing one if the specified file already exists.
      parameters:
        - description: |
            Path to the relevant file (relative to your vault root).
          in: "path"
          name: "filename"
          required: true
          schema:
            format: "path"
            type: "string"
      requestBody:
        content:
          "*/*":
            schema:
              type: "string"
          text/markdown:
            schema:
              example: |
                # This is my document
                
                something else here
              type: "string"
        description: "Content of the file you would like to upload."
        required: true
      responses:
        "204":
          description: "Success"
        "400":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Incoming file could not be processed.  Make sure you have specified a reasonable file name, and make sure you have set a reasonable 'Content-Type' header; if you are uploading a note, 'text/markdown' is likely the right choice.
        "405":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: |
            Your path references a directory instead of a file; this request method is valid only for updating files.
      summary: |
        Create a new file in your vault or update the content of an existing one.
      tags:
        - "Vault Files"
  "/vault/{pathToDirectory}/":
    get:
      parameters:
        - description: |
            Path to list files from (relative to your vault root).  Note that empty directories will not be returned.
            
            Note: this particular interactive tool requires that you provide an argument for this field, but the API itself will allow you to list the root folder of your vault. If you would like to try listing content in the root of your vault using this interactive tool, use the above "List files that exist in the root of your vault" form above.
          in: "path"
          name: "pathToDirectory"
          required: true
          schema:
            format: "path"
            type: "string"
      responses:
        "200":
          content:
            application/json:
              example:
                files:
                  - "mydocument.md"
                  - "somedirectory/"
              schema:
                properties:
                  files:
                    items:
                      type: "string"
                    type: "array"
                type: "object"
          description: "Success"
        "404":
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
          description: "Directory does not exist"
      summary: |
        List files that exist in the specified directory.
      tags:
        - "Vault Directories"
security:
  - apiKeyAuth: []
servers:
  - description: "HTTPS (Secure Mode)"
    url: "https://{host}:{port}"
    variables:
      host:
        default: "127.0.0.1"
        description: "Binding host"
      port:
        default: "27124"
        description: "HTTPS port"
  - description: "HTTP (Insecure Mode)"
    url: "http://{host}:{port}"
    variables:
      host:
        default: "127.0.0.1"
        description: "Binding host"
      port:
        default: "27123"
        description: "HTTP port"
