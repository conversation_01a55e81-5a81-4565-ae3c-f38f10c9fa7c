{
  "mcpServers": {
    "local/github": {
      "name": "GitH<PERSON> (Local)",
      "type": "stdio",
      "command": "docker",
      "args": [
        "run",
        "-i",
        "--rm",
        "-e",
        "GITHUB_PERSONAL_ACCESS_TOKEN",
        "-e",
        "GITHUB_TOOLSETS",
        "-e",
        "GITHUB_READ_ONLY",
        "-e",
        "GITHUB_HOST",
        "ghcr.io/github/github-mcp-server"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}",
        "GITHUB_TOOLSETS": "${GITHUB_TOOLSETS:-all}",
        "GITHUB_READ_ONLY": "${GITHUB_READ_ONLY:-0}",
        "GITHUB_HOST": "${GITHUB_HOST:-https://api.github.com}"
      },
      "description": "GitHub MCP服务器，提供GitHub仓库、问题、PR等操作功能",
      "isActive": true
    },
    "local/filesystem": {
      "name": "Filesystem (Local)",
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/home/<USER>"
      ],
      "description": "文件系统操作服务器，允许读写文件和目录操作",
      "isActive": true
    },
    "local/git": {
      "name": "Git (Local)",
      "type": "stdio",
      "command": "./mcp_env/bin/python",
      "args": [
        "-m",
        "mcp_server_git",
        "--repository",
        "/home/<USER>/mcp"
      ],
      "description": "Git版本控制服务器，提供Git操作功能",
      "isActive": true
    },
    "local/time": {
      "name": "Time (Local)",
      "type": "stdio",
      "command": "./mcp_env/bin/python",
      "args": [
        "-m",
        "mcp_server_time"
      ],
      "description": "时间服务器，提供日期时间相关功能",
      "isActive": true
    },
    "local/sqlite": {
      "name": "SQLite (Local)",
      "type": "stdio",
      "command": "./mcp_env/bin/python",
      "args": [
        "-m",
        "mcp_server_sqlite"
      ],
      "description": "SQLite数据库服务器，提供数据库操作功能",
      "isActive": true
    },
    "local/fetch": {
      "name": "Fetch (Local)",
      "type": "stdio",
      "command": "./mcp_env/bin/python",
      "args": [
        "-m",
        "mcp_server_fetch"
      ],
      "description": "网页抓取服务器，可以获取和解析网页内容",
      "isActive": true
    },
    "local/deepwiki": {
      "name": "DeepWiki (Local)",
      "type": "sse",
      "baseUrl": "https://mcp.deepwiki.com/sse",
      "description": "DeepWiki MCP服务器，提供GitHub仓库文档搜索和AI问答功能",
      "isActive": true
    },
    "9FhoACyk8MMGBL7wm3elg": {
      "name": "Knowledge Graph (mcp-knowledge-graph)",
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "mcp-knowledge-graph",
        "--memory-path",
        "/home/<USER>/.local/share/knowledge-graph/memory.jsonl"
      ],
      "description": "知识图谱记忆服务器，用于存储和检索信息",
      "gallery": true,
      "isActive": true
    },
    "DAqqy1Kdv0n4cSPiyYOmI": {
      "name": "blender",
      "type": "stdio",
      "command": "uvx",
      "args": [
        "blender-mcp"
      ],
      "isActive": true
    },
    "@modelscope/@modelcontextprotocol/sequentialthinking": {
      "name": "Sequential Thinking (ModelScope)",
      "description": "一种MCP服务器实现，它通过结构化的思维过程提供了一种动态且反射性的解决问题的工具。",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/e43cfb115fd44e/sse",
      "isActive": true,
      "provider": "ModelScope",
      "providerUrl": "https://www.modelscope.cn/mcp/servers/@@modelcontextprotocol/sequentialthinking",
      "logoUrl": "https://resources.modelscope.cn/mcp-covers/avatar/modelcontextprotocol.png"
    },
    "@modelscope/@antvis/mcp-server-chart": {
      "name": "可视化图表-MCP-Server",
      "description": "这是一个基于AntV的模型上下文协议服务器，用于生成图表。它支持超过20种类型的图表，并且可以与多种桌面应用程序和平台一起使用。",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/4cee598e62284e/sse",
      "isActive": true,
      "provider": "ModelScope",
      "providerUrl": "https://www.modelscope.cn/mcp/servers/@@antvis/mcp-server-chart",
      "logoUrl": "https://resources.modelscope.cn/mcp-server-cover/ec4a0829-8d46-4709-88e9-67927e639b18.JPG",
      "tags": [
        "antv",
        "visualization"
      ]
    },
    "@modelscope/@regenrek/mcp-deepwiki": {
      "name": "Github的维基百科DeepWiKi (ModelScope)",
      "description": "📖 MCP 服务器用于获取 deepwiki.com 的最新知识，并在 Cursor 和其他代码编辑器中使用",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/fce813a0fcb146/sse",
      "isActive": false,
      "provider": "ModelScope",
      "providerUrl": "https://www.modelscope.cn/mcp/servers/@@regenrek/mcp-deepwiki",
      "logoUrl": "https://resources.modelscope.cn/mcp-server-cover/a0450a13-31e0-48c1-9f61-51309894f02a.png"
    },
    "@modelscope/@yan5236/bing-cn-mcp-server": {
      "name": "必应搜索中文",
      "description": "必应搜索中文",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/82cc7636d8df41/sse",
      "isActive": true,
      "provider": "ModelScope",
      "providerUrl": "https://www.modelscope.cn/mcp/servers/@@yan5236/bing-cn-mcp-server",
      "logoUrl": "https://resources.modelscope.cn/studio-cover-prod/studio-cover_c1a4feec-7dd2-4188-bd72-aebde64758f3.png"
    },
    "@modelscope/@baranwang/mcp-trends-hub": {
      "name": "MCP 中文趋势聚合",
      "description": "一个MCP服务器，聚合了来自新浪微博、知乎、Bilibili等中国各大网站和平台的热门趋势和排行榜。",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/83fa0f3fb0e94e/sse",
      "isActive": true,
      "provider": "ModelScope",
      "providerUrl": "https://www.modelscope.cn/mcp/servers/@@baranwang/mcp-trends-hub",
      "logoUrl": ""
    },
    "@modelscope/@modelcontextprotocol/fetch": {
      "name": "Fetch网页内容抓取 (ModelScope)",
      "description": "该服务器使大型语言模型能够检索和处理网页内容，将HTML转换为markdown格式，以便于更轻松地使用。",
      "type": "sse",
      "baseUrl": "https://mcp-d5bba8e0-44e4-4809.api-inference.modelscope.net/sse",
      "isActive": false,
      "provider": "ModelScope",
      "providerUrl": "https://www.modelscope.cn/mcp/servers/@@modelcontextprotocol/fetch",
      "logoUrl": "https://resources.modelscope.cn/studio-cover-pre/studio-cover_761f7bfe-fc5c-4753-b955-dcdd3288941b.png"
    },
    "72Tt2Jlg_lQiubkRWhvv0": {
      "name": "@cherry/python",
      "type": "inMemory",
      "description": "在安全的沙盒环境中执行 Python 代码。使用 Pyodide 运行 Python，支持大多数标准库和科学计算包",
      "isActive": true,
      "provider": "CherryAI"
    },
    "DN8nKsSoOcSN9o8l84oWH": {
      "name": "@cherry/fetch",
      "type": "inMemory",
      "description": "用于获取 URL 网页内容的 MCP 服务器",
      "isActive": false,
      "provider": "CherryAI"
    },
    "TrkNXWQ-F2PzAVOtGvkhA": {
      "name": "@cherry/filesystem",
      "type": "inMemory",
      "description": "实现文件系统操作的模型上下文协议（MCP）的 Node.js 服务器",
      "isActive": false,
      "provider": "CherryAI",
      "args": [
        "/home/<USER>"
      ]
    },
    "pQU0lCRGCM8zTVQfn9Yt1": {
      "name": "@cherry/sequentialthinking",
      "type": "inMemory",
      "description": "一个 MCP 服务器实现，提供了通过结构化思维过程进行动态和反思性问题解决的工具",
      "isActive": false,
      "provider": "CherryAI"
    },
    "r8eBP6kg14AtN4Nqq0f2I": {
      "name": "@cherry/mcp-auto-install",
      "description": "自动安装 MCP 服务（测试版）https://docs.cherry-ai.com/advanced-basic/mcp/auto-install",
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@mcpmarket/mcp-auto-install",
        "connect",
        "--json"
      ],
      "isActive": true,
      "provider": "CherryAI"
    },
    "sdhsVmsvNgaGq8dOEIPY3": {
      "name": "chrome-mcp-server",
      "type": "streamableHttp",
      "description": "",
      "isActive": true,
      "longRunning": true,
      "baseUrl": "http://127.0.0.1:12306/mcp"
    }
  }
}




{
  "mcpServers": {
    "obsidian": {
      "command": "uv",
      "args": [
        "--directory",
        "/home/<USER>/mcp/mcp-obsidian",
        "run",
        "mcp-obsidian"
      ],
      "env": {
        "OBSIDIAN_API_KEY": "6ad3a9c097110a06208ae134658d5d32680db21ef6b16817d833ece7cf86c45a",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      },
      "description": "Obsidian MCP服务器，通过Local REST API与Obsidian交互"
    }
  }
}




{
  "mcpServers": {
    "filesystem": {
      "name": "Filesystem (Local)",
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/home/<USER>"
      ],
      "description": "文件系统操作服务器，限制在用户主目录",
      "isActive": true
    },
    "knowledge-graph": {
      "name": "Knowledge Graph (Local)",
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "mcp-knowledge-graph",
        "--memory-path",
        "/home/<USER>/.local/share/knowledge-graph/memory.jsonl"
      ],
      "description": "知识图谱记忆服务器",
      "isActive": true
    },
    "sequential-thinking": {
      "name": "Sequential Thinking (Local)",
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ],
      "description": "本地顺序思考服务器",
      "isActive": true
    },
    "puppeteer": {
      "name": "Puppeteer",
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-puppeteer"
      ],
      "env": {},
      "description": "Puppeteer-based browser automation server",
      "isActive": false
    },
    "github": {
      "name": "GitHub (Local)",
      "type": "stdio",
      "command": "docker",
      "args": [
        "run",
        "-i",
        "--rm",
        "-e",
        "GITHUB_PERSONAL_ACCESS_TOKEN",
        "-e",
        "GITHUB_TOOLSETS",
        "-e",
        "GITHUB_READ_ONLY",
        "-e",
        "GITHUB_HOST",
        "ghcr.io/github/github-mcp-server"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}"
      },
      "description": "GitHub MCP服务器，请在环境变量中配置GITHUB_PERSONAL_ACCESS_TOKEN",
      "isActive": true
    },
    "deepwiki": {
      "name": "DeepWiki",
      "description": "DeepWiki MCP服务器，提供GitHub仓库文档搜索和AI问答功能",
      "type": "sse",
      "baseUrl": "https://mcp.deepwiki.com/sse",
      "isActive": true
    },
    "bing-search-cn": {
      "name": "必应搜索中文 (ModelScope)",
      "description": "必应搜索中文",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/82cc7636d8df41/sse",
      "isActive": true,
      "provider": "ModelScope",
      "providerUrl": "https://www.modelscope.cn/mcp/servers/@@yan5236/bing-cn-mcp-server",
      "logoUrl": "https://resources.modelscope.cn/studio-cover-prod/studio-cover_c1a4feec-7dd2-4188-bd72-aebde64758f3.png"
    },
    "antvis-charts": {
      "name": "可视化图表-MCP-Server (ModelScope)",
      "description": "这是一个基于AntV的模型上下文协议服务器，用于生成图表。",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/4cee598e62284e/sse",
      "isActive": true,
      "provider": "ModelScope",
      "providerUrl": "https://www.modelscope.cn/mcp/servers/@@antvis/mcp-server-chart",
      "logoUrl": "https://resources.modelscope.cn/mcp-server-cover/ec4a0829-8d46-4709-88e9-67927e639b18.JPG",
      "tags": [
        "antv",
        "visualization"
      ]
    },
    "cherry-python": {
      "name": "Python Sandbox (@cherry/python)",
      "type": "inMemory",
      "description": "在安全的沙盒环境中执行 Python 代码。",
      "isActive": true,
      "provider": "CherryAI"
    },
    "chrome-mcp-server": {
      "name": "Chrome MCP Server",
      "type": "streamableHttp",
      "description": "与本地Chrome浏览器实例连接的服务器",
      "isActive": true,
      "longRunning": true,
      "baseUrl": "http://127.0.0.1:12306/mcp"
    },
    "@modelscope/@modelcontextprotocol/sequentialthinking": {
      "name": "Sequential Thinking (ModelScope)",
      "description": "一种MCP服务器实现，它通过结构化的思维过程提供了一种动态且反射性的解决问题的工具。",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/e43cfb115fd44e/sse",
      "isActive": false,
      "provider": "ModelScope"
    },
    "@modelscope/@regenrek/mcp-deepwiki": {
      "name": "Github的维基百科DeepWiKi (ModelScope)",
      "description": "📖 MCP 服务器用于获取 deepwiki.com 的最新知识，并在 Cursor 和其他代码编辑器中使用",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/fce813a0fcb146/sse",
      "isActive": false,
      "provider": "ModelScope"
    },
    "@modelscope/@tokenizin-agency/mcp-npx-fetch": {
      "name": "内容抓取转换器 (ModelScope)",
      "description": "一个强大的MCP服务器，可以轻松地将网页内容抓取并转换为各种格式（HTML、JSON、Markdown、纯文本）。",
      "type": "sse",
      "baseUrl": "https://mcp.api-inference.modelscope.net/6e3903fb098e4a/sse",
      "isActive": false,
      "provider": "ModelScope"
    },
    "mcp-neo4j-agent-memory": {
      "name": "Neo4j Agent Memory",
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@knowall-ai/mcp-neo4j-agent-memory",
        "--key",
        "2245df3b-b431-499d-83cc-6c2215eb2a24",
        "--profile",
        "adamant-dress-DqkJs8"
      ],
      "description": "Neo4j Agent Memory Server",
      "isActive": false
    },
    "obsidian": {
      "name": "Obsidian",
      "type": "stdio",
      "command": "uv",
      "args": [
        "--directory",
        "/home/<USER>/mcp/mcp-obsidian",
        "run",
        "mcp-obsidian"
      ],
      "env": {
        "OBSIDIAN_API_KEY": "6ad3a9c097110a06208ae134658d5d32680db21ef6b16817d833ece7cf86c45a",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      },
      "description": "Obsidian MCP服务器，通过Local REST API与Obsidian交互",
      "isActive": true
    },
    "desktop-commander": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@wonderwhy-er/desktop-commander",
        "--key",
        "2245df3b-b431-499d-83cc-6c2215eb2a24"
      ]
    },
    "context7-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@upstash/context7-mcp",
        "--key",
        "2245df3b-b431-499d-83cc-6c2215eb2a24"
      ]
    },
    "mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@docfork/mcp",
        "--key",
        "2245df3b-b431-499d-83cc-6c2215eb2a24"
      ]
    }
  }
}