.# Obsidian MCP 集成指南

本指南将帮助您配置和使用MCP (Model Context Protocol) 服务器与Obsidian笔记应用集成。

## 📋 概述

通过MCP协议，您可以将AI助手（如Claude Desktop）与Obsidian笔记应用连接起来，实现：

- 在AI对话中直接访问和操作Obsidian笔记
- 使用AI工具管理和组织您的知识库
- 在Obsidian中执行自动化任务

## 🚀 快速开始

### 1. 启动MCP服务器

```bash
./start_obsidian_mcp.sh
```

这将启动以下MCP服务器：

- **filesystem**: 文件系统操作
- **memory**: 知识图谱记忆
- **sequential-thinking**: 顺序思考
- **git**: Git版本控制
- **time**: 时间服务
- **obsidian-tools**: Obsidian专用工具（如果已安装）

### 2. 配置Obsidian

1. 确保已安装Obsidian应用
2. 安装必要的插件：
   - Local REST API Plugin（用于API访问）
3. 在Obsidian设置中配置MCP服务器

### 3. 配置AI助手（如Claude Desktop）

将 [obsidian_mcp_config.json](file:///home/<USER>/mcp/obsidian_mcp_config.json) 中的配置添加到您的AI助手MCP设置中。

## 🛠️ 详细配置

### Obsidian插件配置

1. 打开Obsidian设置
2. 进入"社区插件"部分
3. 禁用"安全模式"
4. 点击"浏览"并搜索"Local REST API"
5. 安装并启用该插件
6. 在插件设置中配置API端口（默认为27123）

### MCP服务器配置

配置文件: [obsidian_mcp_config.json](file:///home/<USER>/mcp/obsidian_mcp_config.json)

此文件包含以下MCP服务器配置：

- filesystem: 文件系统操作，允许读写文件和目录操作
- memory: 知识图谱记忆，用于存储和检索信息
- sequential-thinking: 顺序思考，帮助结构化问题解决过程
- local-filesystem: 本地文件系统服务器（自定义实现）
- local-memory: 本地记忆服务器（自定义实现）
- git: Git版本控制服务器，用于管理笔记的版本历史
- time: 时间服务器，提供时间相关功能
- obsidian-tools: Obsidian专用工具集（需要单独安装）

## ▶️ 使用方法

### 启动服务器

```bash
./start_obsidian_mcp.sh
```

### 停止服务器

```bash
./stop_obsidian_mcp.sh
```

### 检查服务器状态

```bash
python start_mcp_servers.py status
```

## 🧪 测试集成

1. 启动MCP服务器
2. 打开您的AI助手（如Claude Desktop）
3. 尝试询问关于您Obsidian笔记的问题
4. 使用AI工具创建、修改或搜索笔记

## 📁 目录结构

```
/home/<USER>/mcp/
├── obsidian_mcp_config.json     # Obsidian MCP配置文件
├── start_obsidian_mcp.sh        # 启动脚本
├── stop_obsidian_mcp.sh         # 停止脚本
├── mcp_env/                     # Python虚拟环境
├── mcp_servers/                 # 自定义MCP服务器实现
└── start_mcp_servers.py         # 服务器管理脚本
```

## 🔧 故障排除

### 服务器无法启动

1. 检查是否已运行 `setup_mcp.sh` 安装所有依赖
2. 确保端口未被占用
3. 检查防火墙设置

### Obsidian无法连接到MCP服务器

1. 确保MCP服务器正在运行
2. 检查配置文件中的端口设置
3. 验证AI助手的MCP配置是否正确

### 权限问题

1. 确保脚本具有执行权限
2. 检查文件和目录的访问权限
3. 在Linux/macOS上可能需要使用 `chmod` 命令调整权限

## 📚 相关资源

- [MCP官方文档](https://modelcontextprotocol.io)
- [Obsidian官方文档](https://help.obsidian.md)
- [Local REST API插件](https://github.com/coddingtonbear/obsidian-local-rest-api)
